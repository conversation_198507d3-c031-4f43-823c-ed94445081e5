import React, { Fragment, useEffect, useState } from 'react';
import UpdateHost from '../modal/UpdateHost';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { But<PERSON>, Spin } from 'antd';
import BackendInitProgress from '../components/BackendInitProgress';


const SystemInitializing = (props) => {
  const {
    systemInitiated,
    initiatingText,
    isFetching,
    isError,
    isLoading,
    isUninitialized,
  } = props;

  const navigate = useNavigate();

  const { t } = useTranslation();

  const [isUpdateHostOpened, setIsUpdateHostOpened] = useState(false);  

  useEffect(() => {
    if (systemInitiated) {
      setIsUpdateHostOpened(false);
      navigate('/home');
    }
  }, [systemInitiated]);

  useEffect(() => {
    if (systemInitiated) {
      setIsUpdateHostOpened(true);
      navigate('/home');
    }
  }, []);

  return (
    <Fragment>
      <UpdateHost
        isOpened={isUpdateHostOpened}
        setIsOpened={setIsUpdateHostOpened}
      />
      <div
        className='flex w-full h-full self-stretch justify-center items-center flex-col gap-2'
      >
        {(isFetching || isLoading || isUninitialized) ?
          <BackendInitProgress
            isActive={true}
          />
        :
          <img
            src='/icn/warning_red.svg'
            className='w-[24px] h-[30px]'
            alt='warning'
          />
        }
        <span className='font-source text-[14px] font-normal leading-[150%]'>
          {initiatingText}
        </span>
        {isError &&
          <div className='flex items-center justify-center gap-2 self-stretch'>
            <Button
              onClick={() => {
                window.location.reload();
              }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('common.refresh')}
              </span>
            </Button>
            <Button
              onClick={() => {
                setIsUpdateHostOpened(true);
              }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('login.changeHostAddress')}
              </span>
            </Button>
          </div>
        }
      </div>
    </Fragment>
  );
};

export default SystemInitializing;