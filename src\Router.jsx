import React, { Fragment, useEffect, useRef, useState } from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import ContainerLayout from './layout/ContainerLayout';
import Login from './pages/Login';
import Home from './pages/Home';
import Settings from './pages/settings';
import Teach from './pages/teach/Index';
import Worklist from './pages/worklist/Worklist';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import UploadCAD from './pages/teach/components/markAlignPCB/uploadCAD/Index';
import { useGetSystemMetadataQuery } from './services/system';
import { useTranslation } from 'react-i18next';
import Live from './pages/inspection/Live';
import Review from './pages/inspection/Review';
import { accessTokenExpCheckInterval, localStorageKeys, serverHost } from './common/const';
import { ALERT_TYPES, aoiAlert } from './common/alert';
import _ from 'lodash';
import { decode } from 'jsonwebtoken';
import AutoProgramming from './pages/autoProgramming/Index';
import { Button, Spin } from 'antd';
import UpdateHost from './modal/UpdateHost';
import LandingVideo from './pages/LandingVideo';


const Router = () => {
  const { t } = useTranslation();

  const userTokenExpCheck = useRef(null);

  const [systemInitiated, setSystemInitiated] = useState(false);
  const [initiatingText, setInitiatingText] = useState('Fetching system metadata...');
  const [isUpdateHostOpened, setIsUpdateHostOpened] = useState(false);

  const { isFetching, isError, isLoading, isUninitialized, isSuccess } = useGetSystemMetadataQuery();

  const initTokenExpCheck = () => {
    userTokenExpCheck.current = setInterval(() => {
      // if delta is positive and less then accessTokenExpCheckInterval then renew the token
      // if delta is negative then redirect to login page
      const curExp = localStorage.getItem(localStorageKeys.tokenExp);
      // curExp ex. **********
      if (!curExp) return;
      const delta = parseInt(curExp) - Math.floor(Date.now() / 1000);
      if (delta <= 0) {
        if (!_.includes(['/login'], window.location.pathname)) window.location.href = '/login';
        return;
      }
      if (delta < accessTokenExpCheckInterval) {
        // renew token
        fetch(`${serverHost}/account/renewAuthentication`, {
          method: 'PUT',
          headers: {
            Authorization: localStorage.getItem(localStorageKeys.accessToken),
          },
        }).then((res) => {
          if (!res.ok) {
            console.error('Failed to renew token');
            return;
          }
          const token = res.headers.get('Authorization');
          localStorage.setItem(localStorageKeys.accessToken, token);
          const decoded = decode(_.split(token, ' ')[1]);
          localStorage.setItem(localStorageKeys.tokenExp, decoded.exp);
        }).catch((err) => {
          aoiAlert(t('notification.error.renewAccessToken'), ALERT_TYPES.COMMON_ERROR);
          return;
        });
      }
    }, [accessTokenExpCheckInterval]);
  };

  useEffect(() => {
    if (isSuccess) {
      setSystemInitiated(true);
      return;
    } else if (isError) {
      setInitiatingText(t('common.failToLoadSystemMetadata'));
    } else {
      setInitiatingText(t('common.fetchingSystemMetadata'));
    }
    setSystemInitiated(false);
  }, [
    isFetching,
    isError,
    isLoading,
    isUninitialized,
    isSuccess,
  ]);

  useEffect(() => {
    if (!systemInitiated) return;

    initTokenExpCheck();

    return () => {
      if (userTokenExpCheck.current) {
        clearInterval(userTokenExpCheck.current);
        userTokenExpCheck.current = null;
      }
    };
  }, [systemInitiated]);

  useEffect(() => {
    if (_.isEmpty(localStorage.getItem(localStorageKeys.accessToken)) && !_.includes(['/login'], window.location.pathname)) {
      // if accessToken is not set, redirect to login page
      window.location.href = '/login';
    }
  }, []);

  if (!systemInitiated) {
    return <Fragment>
      <UpdateHost
        isOpened={isUpdateHostOpened}
        setIsOpened={setIsUpdateHostOpened}
      />
      <div
        className='flex w-full h-full self-stretch justify-center items-center flex-col gap-2'
      >
        {(isFetching || isLoading || isUninitialized) ?
          <Spin
            size='large'
          />
        :
          <img
            src='/icn/warning_red.svg'
            className='w-[24px] h-[30px]'
            alt='warning'
          />
        }
        <span className='font-source text-[14px] font-normal leading-[150%]'>
          {initiatingText}
        </span>
        {isError &&
          <div className='flex items-center justify-center gap-2 self-stretch'>
            <Button
              onClick={() => {
                window.location.reload();
              }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('common.refresh')}
              </span>
            </Button>
            <Button
              onClick={() => {
                setIsUpdateHostOpened(true);
              }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('login.changeHostAddress')}
              </span>
            </Button>
          </div>
        }
      </div>
    </Fragment>;
  }

  return (
    <BrowserRouter>
      <ContainerLayout>
        <Routes>
          {/* <Route path='/landing-video' element={<LandingVideo />} /> */}
          <Route path='/' element={<Home />} />
          <Route path='/login' element={<Login />} />
          <Route path='/home' element={<Home />} />
          <Route path='/settings' element={<Settings />} />
          <Route path='/teach' element={<Teach />} />
          <Route exact path='/teach/uploadCAD' element={<UploadCAD />} />
          <Route path='/worklist' element={<Worklist />} />
          <Route path='/inspection/live' element={<Live />} />
          <Route path='/inspection/review' element={<Review />} />
          <Route path='/auto-programming-setup' element={<AutoProgramming />} />
        </Routes>
      </ContainerLayout>
      <ToastContainer />
    </BrowserRouter>
  );
};

export default Router;